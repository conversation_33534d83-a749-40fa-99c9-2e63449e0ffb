<?php

namespace App\Http\Controllers;

use App\Models\OrganizationRequest;
use App\Models\User;
use App\Notifications\OrganizationRequestSubmitted;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class OrganizationRequestController extends Controller
{
    /**
     * Display the organization request form
     */
    public function create()
    {
        $user = auth()->user();

        // Check if user already has a pending request
        $pendingRequest = $user->organizationRequests()->pending()->first();

        if ($pendingRequest) {
            return redirect()->route('organization-requests.show', $pendingRequest)
                ->with('info', 'You already have a pending organization request.');
        }

        return view('organization-requests.create');
    }

    /**
     * Store a new organization request
     */
    public function store(Request $request)
    {
        $user = auth()->user();

        // Check if user already has a pending request
        if ($user->organizationRequests()->pending()->exists()) {
            return back()->with('error', 'You already have a pending organization request.');
        }

        $validated = $request->validate([
            'organization_name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('organizations', 'name'),
                Rule::unique('organization_requests', 'organization_name')
                    ->where('status', 'pending')
            ],
            'description' => 'required|string|min:50|max:1000',
            'proof_document' => 'required|file|mimes:pdf,jpg,jpeg,png|max:5120', // 5MB max
        ]);

        // Handle proof document upload
        if ($request->hasFile('proof_document')) {
            $validated['proof_document'] = $request->file('proof_document')
                ->store('organization-requests/proofs', 'public');
        }

        $validated['user_id'] = $user->id;

        $organizationRequest = OrganizationRequest::create($validated);

        // Notify all admins about the new request
        $admins = User::where('role', 'admin')->get();
        foreach ($admins as $admin) {
            $admin->notify(new OrganizationRequestSubmitted($organizationRequest));
        }

        return redirect()->route('organization-requests.show', $organizationRequest)
            ->with('success', 'Your organization request has been submitted successfully! You will be notified once it is reviewed.');
    }

    /**
     * Display a specific organization request
     */
    public function show(OrganizationRequest $organizationRequest)
    {
        // Only allow the requester to view their own request
        if ($organizationRequest->user_id !== auth()->id()) {
            abort(403, 'Unauthorized access to organization request.');
        }

        return view('organization-requests.show', compact('organizationRequest'));
    }

    /**
     * Display user's organization requests
     */
    public function index()
    {
        $user = auth()->user();

        $requests = $user->organizationRequests()
            ->with(['reviewer', 'createdOrganization'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('organization-requests.index', compact('requests'));
    }
}
