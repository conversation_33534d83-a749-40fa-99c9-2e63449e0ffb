<x-unilink-layout>
    <!-- <PERSON>er -->
    <div class="mb-8">
        <div class="flex items-center space-x-4">
            <a href="{{ route('organizations.show', $organization) }}" class="text-gray-400 hover:text-gray-600">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Officer Management</h1>
                <p class="text-gray-600 mt-1">{{ $organization->name }}</p>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Current Officers -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900">Current Officers</h2>
                </div>
                
                <div class="divide-y divide-gray-200">
                    @forelse($organization->activeMembers->where('pivot.role', '!=', 'member') as $member)
                        <div class="p-6 flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <img src="{{ $member->getAvatarUrl(48) }}" 
                                     alt="{{ $member->name }}"
                                     class="w-12 h-12 rounded-full">
                                <div>
                                    <h3 class="text-sm font-medium text-gray-900">{{ $member->name }}</h3>
                                    <p class="text-sm text-gray-500">{{ $member->email }}</p>
                                    <div class="flex items-center mt-1">
                                        @if($member->pivot->role === 'president')
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd" d="M9.664 1.319a.75.75 0 01.672 0 41.059 41.059 0 018.198 5.424.75.75 0 01-.254 1.285 31.372 31.372 0 00-7.86 3.83.75.75 0 01-.84 0 31.508 31.508 0 00-2.08-1.287V9.394c0-.244.116-.463.302-.592a35.504 35.504 0 013.305-*********** 0 00-.714-1.319 37 37 0 00-3.446 2.12A2.216 2.216 0 006 9.393v.38a31.293 31.293 0 00-4.28-1.746.75.75 0 01-.254-1.285 41.059 41.059 0 018.198-5.424zM6 11.459a29.848 29.848 0 00-2.455-1.158 41.029 41.029 0 00-.39 *********** 0 00.419.74c.528.256 1.046.53 1.554.82-.21-.899-.455-1.746-.754-2.516zm9.909-1.158A29.848 29.848 0 0014 11.459c-.299.77-.544 1.617-.754 2.516.508-.29 1.026-.564 1.554-.82a.75.75 0 00.419-.74 41.029 41.029 0 00-.39-3.114z" clip-rule="evenodd" />
                                                </svg>
                                                President
                                            </span>
                                        @else
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                {{ $member->pivot->custom_role_title ?: match($member->pivot->role) {
                                                    'vice_president' => 'Vice President',
                                                    'secretary' => 'Secretary',
                                                    'treasurer' => 'Treasurer',
                                                    'officer' => 'Officer',
                                                    default => ucfirst(str_replace('_', ' ', $member->pivot->role))
                                                } }}
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            
                            @if($member->pivot->role !== 'president')
                                <div class="flex items-center space-x-2">
                                    <button onclick="editOfficer({{ $member->id }}, '{{ $member->pivot->role }}', '{{ $member->pivot->custom_role_title }}')"
                                            class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                                        Edit
                                    </button>
                                    <form action="{{ route('organizations.remove-officer', $organization) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <input type="hidden" name="user_id" value="{{ $member->id }}">
                                        <button type="submit" 
                                                onclick="return confirm('Are you sure you want to remove this officer?')"
                                                class="text-red-600 hover:text-red-900 text-sm font-medium">
                                            Remove
                                        </button>
                                    </form>
                                </div>
                            @endif
                        </div>
                    @empty
                        <div class="p-6 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No officers appointed</h3>
                            <p class="mt-1 text-sm text-gray-500">Start by appointing officers from your members.</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>

        <!-- Add Officer Form -->
        <div class="space-y-6">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Appoint Officer</h3>
                
                <form action="{{ route('organizations.add-officer', $organization) }}" method="POST" class="space-y-4">
                    @csrf
                    
                    <div>
                        <label for="user_id" class="block text-sm font-medium text-gray-700 mb-1">Select Member</label>
                        <div class="relative" x-data="memberSelector()">
                            <button @click="toggleDropdown()" type="button"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-left bg-white flex items-center justify-between">
                                <span x-text="selectedMember ? selectedMember.name : 'Choose a member...'"
                                      :class="selectedMember ? 'text-gray-900' : 'text-gray-500'"></span>
                                <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>

                            <input type="hidden" id="user_id" name="user_id" x-model="selectedMemberId" required>

                            <div x-show="open" @click.away="open = false" x-transition
                                 class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none">

                                <!-- Search input -->
                                <div class="sticky top-0 bg-white p-2 border-b">
                                    <input x-model="searchQuery" @input="filterMembers()" type="text"
                                           placeholder="Search members..."
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                                </div>

                                <!-- Member list -->
                                <template x-for="member in filteredMembers" :key="member.id">
                                    <div @click="selectMember(member)"
                                         class="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-50 flex items-center space-x-3">
                                        <img :src="member.avatar_url" :alt="member.name"
                                             class="w-8 h-8 rounded-full flex-shrink-0">
                                        <div class="flex-1 min-w-0">
                                            <p class="text-sm font-medium text-gray-900 truncate" x-text="member.name"></p>
                                            <p class="text-xs text-gray-500 truncate" x-text="member.email"></p>
                                        </div>
                                        <div x-show="selectedMemberId == member.id" class="text-blue-600">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                    </div>
                                </template>

                                <div x-show="filteredMembers.length === 0" class="py-4 text-center text-sm text-gray-500">
                                    No members found
                                </div>
                            </div>
                        </div>
                        @error('user_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div x-data="{ roleType: 'predefined' }">
                        <label class="block text-sm font-medium text-gray-700 mb-3">Officer Role</label>

                        <!-- Role Type Selection -->
                        <div class="flex space-x-4 mb-4">
                            <label class="flex items-center">
                                <input type="radio" x-model="roleType" value="predefined" class="form-radio text-blue-600">
                                <span class="ml-2 text-sm text-gray-700">Predefined Role</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" x-model="roleType" value="custom" class="form-radio text-blue-600">
                                <span class="ml-2 text-sm text-gray-700">Custom Role</span>
                            </label>
                        </div>

                        <!-- Predefined Role Selection -->
                        <div x-show="roleType === 'predefined'" x-transition>
                            <select id="role" name="role" :required="roleType === 'predefined'"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent mb-3">
                                <option value="">Select role...</option>
                                @foreach($availableRoles as $value => $label)
                                    <option value="{{ $value }}">{{ $label }}</option>
                                @endforeach
                            </select>

                            <div>
                                <label for="custom_role_title" class="block text-sm font-medium text-gray-700 mb-1">Custom Title (Optional)</label>
                                <input type="text" id="custom_role_title" name="custom_role_title"
                                       placeholder="e.g., Senior Vice President, Executive Secretary"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <p class="mt-1 text-xs text-gray-500">Override the default role title with a custom one</p>
                            </div>
                        </div>

                        <!-- Custom Role Creation -->
                        <div x-show="roleType === 'custom'" x-transition>
                            <input type="hidden" name="role" x-bind:value="roleType === 'custom' ? 'officer' : ''">
                            <div>
                                <label for="custom_role_title_full" class="block text-sm font-medium text-gray-700 mb-1">Custom Role Title *</label>
                                <input type="text" id="custom_role_title_full" name="custom_role_title"
                                       :required="roleType === 'custom'"
                                       placeholder="e.g., Marketing Director, Event Coordinator, Project Manager"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <p class="mt-1 text-xs text-gray-500">Create a completely custom officer role</p>
                            </div>

                            <div class="mt-3 p-3 bg-blue-50 rounded-lg">
                                <p class="text-sm text-blue-700">
                                    <strong>Custom Role Examples:</strong> Marketing Director, Event Coordinator, Project Manager,
                                    Public Relations Officer, Finance Manager, Academic Affairs Head, Sports Coordinator, etc.
                                </p>
                            </div>
                        </div>

                        @error('role')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                        @error('custom_role_title')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <button type="submit" 
                            class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        Appoint Officer
                    </button>
                </form>
            </div>

            <!-- Regular Members -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Regular Members</h3>
                
                <div class="space-y-3 max-h-64 overflow-y-auto">
                    @forelse($organization->activeMembers->where('pivot.role', 'member') as $member)
                        <div class="flex items-center space-x-3">
                            <img src="{{ $member->getAvatarUrl(32) }}" 
                                 alt="{{ $member->name }}"
                                 class="w-8 h-8 rounded-full">
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">{{ $member->name }}</p>
                                <p class="text-xs text-gray-500 truncate">{{ $member->email }}</p>
                            </div>
                        </div>
                    @empty
                        <p class="text-sm text-gray-500">No regular members available.</p>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Officer Modal -->
    <div id="editOfficerModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Edit Officer</h3>
                
                <form id="editOfficerForm" method="POST" class="space-y-4">
                    @csrf
                    <input type="hidden" id="edit_user_id" name="user_id">
                    
                    <div>
                        <label for="edit_role" class="block text-sm font-medium text-gray-700 mb-1">Officer Role</label>
                        <select id="edit_role" name="role" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            @foreach($availableRoles as $value => $label)
                                <option value="{{ $value }}">{{ $label }}</option>
                            @endforeach
                        </select>
                    </div>

                    <div>
                        <label for="edit_custom_role_title" class="block text-sm font-medium text-gray-700 mb-1">Custom Title (Optional)</label>
                        <input type="text" id="edit_custom_role_title" name="custom_role_title" 
                               placeholder="e.g., Marketing Director, Event Coordinator"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeEditModal()" 
                                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">
                            Update Officer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function editOfficer(userId, role, customTitle) {
            document.getElementById('edit_user_id').value = userId;
            document.getElementById('edit_role').value = role;
            document.getElementById('edit_custom_role_title').value = customTitle || '';
            document.getElementById('editOfficerForm').action = '{{ route("organizations.add-officer", $organization) }}';
            document.getElementById('editOfficerModal').classList.remove('hidden');
        }

        function closeEditModal() {
            document.getElementById('editOfficerModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('editOfficerModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeEditModal();
            }
        });

        // Member selector component
        function memberSelector() {
            return {
                open: false,
                searchQuery: '',
                selectedMember: null,
                selectedMemberId: '',
                members: @json($organization->activeMembers->where('pivot.role', 'member')->map(function($member) {
                    return [
                        'id' => $member->id,
                        'name' => $member->name,
                        'email' => $member->email,
                        'avatar_url' => $member->getAvatarUrl(32)
                    ];
                })->values()),
                filteredMembers: [],

                init() {
                    this.filteredMembers = this.members;
                },

                toggleDropdown() {
                    this.open = !this.open;
                    if (this.open) {
                        this.searchQuery = '';
                        this.filterMembers();
                    }
                },

                selectMember(member) {
                    this.selectedMember = member;
                    this.selectedMemberId = member.id;
                    this.open = false;
                },

                filterMembers() {
                    if (!this.searchQuery) {
                        this.filteredMembers = this.members;
                        return;
                    }

                    const query = this.searchQuery.toLowerCase();
                    this.filteredMembers = this.members.filter(member =>
                        member.name.toLowerCase().includes(query) ||
                        member.email.toLowerCase().includes(query)
                    );
                }
            }
        }
    </script>
</x-unilink-layout>
